#include <iostream>
#include <cmath>
using namespace std;

// 抽象基类：几何图形
class Geometric_shape {
public:
    virtual void Show() = 0;  // 显示图形信息
    virtual double perimeter() = 0;  // 周长
    virtual double area() = 0;  // 面积
    virtual double volume() = 0;  // 体积
    virtual ~Geometric_shape() {}  // 虚析构函数
};

// 平面图形：圆形
class Circle : public Geometric_shape {
private:
    double radius;
public:
    Circle(double r) : radius(r) {}

    void Show() override {
        cout << "Circle: radius = " << radius;
    }

    double perimeter() override {
        return 2 * M_PI * radius;
    }

    double area() override {
        return M_PI * radius * radius;
    }

    double volume() override {
        return 0;  // 平面图形体积为0
    }
};

// 平面图形：矩形
class Rectangle : public Geometric_shape {
private:
    double length, width;
public:
    Rectangle(double l, double w) : length(l), width(w) {}

    void Show() override {
        cout << "Rectangle: length = " << length << ", width = " << width;
    }

    double perimeter() override {
        return 2 * (length + width);
    }

    double area() override {
        return length * width;
    }

    double volume() override {
        return 0;  // 平面图形体积为0
    }
};

// 平面图形：三角形
class Triangle : public Geometric_shape {
private:
    double a, b, c;
public:
    Triangle(double side1, double side2, double side3) : a(side1), b(side2), c(side3) {}

    void Show() override {
        cout << "Triangle: sides = " << a << ", " << b << ", " << c;
    }

    double perimeter() override {
        return a + b + c;
    }

    // 使用海伦公式计算三角形面积
    double area() override {
        double s = (a + b + c) / 2;
        return sqrt(s * (s-a) * (s-b) * (s-c));
    }

    double volume() override {
        return 0;  // 平面图形体积为0
    }
};

// 立体图形：长方体
class Box : public Geometric_shape {
private:
    double length, width, height;
public:
    Box(double l, double w, double h) : length(l), width(w), height(h) {}

    void Show() override {
        cout << "Box: length = " << length << ", width = " << width << ", height = " << height;
    }

    // 底面（矩形）周长
    double perimeter() override {
        return 2 * (length + width);
    }

    // 底面（矩形）面积
    double area() override {
        return length * width;
    }

    double volume() override {
        return length * width * height;
    }
};

// 立体图形：圆柱体
class Cylinder : public Geometric_shape {
private:
    double radius, height;
public:
    Cylinder(double r, double h) : radius(r), height(h) {}

    void Show() override {
        cout << "Cylinder: radius = " << radius << ", height = " << height;
    }

    // 底面（圆）周长
    double perimeter() override {
        return 2 * M_PI * radius;
    }

    // 底面（圆）面积
    double area() override {
        return M_PI * radius * radius;
    }

    double volume() override {
        return M_PI * radius * radius * height;
    }
};

// 立体图形：圆锥体
class Cone : public Geometric_shape {
private:
    double radius, height;
public:
    Cone(double r, double h) : radius(r), height(h) {}

    void Show() override {
        cout << "Cone: radius = " << radius << ", height = " << height;
    }

    // 底面（圆）周长
    double perimeter() override {
        return 2 * M_PI * radius;
    }

    // 底面（圆）面积
    double area() override {
        return M_PI * radius * radius;
    }

    double volume() override {
        return (1.0/3) * M_PI * radius * radius * height;
    }
};

// 立体图形：三棱锥
class T_pyramid : public Geometric_shape {
private:
    double a, b, c, height;
public:
    T_pyramid(double side1, double side2, double side3, double h)
        : a(side1), b(side2), c(side3), height(h) {}

    void Show() override {
        cout << "T_pyramid: sides = " << a << ", " << b << ", " << c << ", height = " << height;
    }

    // 底面（三角形）周长
    double perimeter() override {
        return a + b + c;
    }

    // 底面（三角形）面积
    double area() override {
        double s = (a + b + c) / 2;
        return sqrt(s * (s-a) * (s-b) * (s-c));
    }

    double volume() override {
        double baseArea = area();
        return (1.0/3) * baseArea * height;
    }
};

// 立体图形：三棱柱
class T_prism : public Geometric_shape {
private:
    double a, b, c, height;
public:
    T_prism(double side1, double side2, double side3, double h)
        : a(side1), b(side2), c(side3), height(h) {}

    void Show() override {
        cout << "T_prism: sides = " << a << ", " << b << ", " << c << ", height = " << height;
    }

    // 底面（三角形）周长
    double perimeter() override {
        return a + b + c;
    }

    // 底面（三角形）面积
    double area() override {
        double s = (a + b + c) / 2;
        return sqrt(s * (s-a) * (s-b) * (s-c));
    }

    double volume() override {
        double baseArea = area();
        return baseArea * height;
    }
};

int main()
{
    Geometric_shape *gs[]= { new Circle(10),
    new  Rectangle(6,8),
    new  Triangle(3,4,5),
    new  Box(6,8,3),
    new   Cylinder(10,3),
    new  Cone(10,3),
    new  T_pyramid(3,4,5,3),
    new  T_prism(3,4,5,3) };

    for (int i=0;i<8;i++)
    {
       gs[i]->Show();
       cout<<endl;
    }

    for (int i=0;i<8;i++)
    {
        gs[i]->Show();
        cout<<endl;
    }

    cout<<"平面图形："<<endl;
    for (int i=0;i<3;i++)
    {
        cout<<"图形周长："<<gs[i]->perimeter()<<'\t';
       cout<<"图形面积："<<gs[i]->area()<<'\t';
       cout<<"图形体积："<<gs[i]->volume()<<endl;
    }

    cout<<"立体图形："<<endl;
    for (int i=3;i<8;i++)
    {
       cout<<"图形底周长："<<gs[i]->perimeter()<<'\t';
       cout<<"图形底面积："<<gs[i]->area()<<'\t';
       cout<<"图形体积  ："<<gs[i]->volume()<<endl;
    }

    // 内存释放
    for (int i=0;i<8;i++)
    {
        delete gs[i];
    }

    return 0;
}