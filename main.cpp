#include <iostream>
#include <cmath>
#include <vector>
#include <memory>
using namespace std;

// 抽象基类：几何图形
class Geometric_shape {
public:
    virtual void Show() = 0;  // 显示图形信息
    virtual double perimeter() = 0;  // 周长
    virtual double area() = 0;  // 面积
    virtual double volume() = 0;  // 体积
    virtual ~Geometric_shape() {}  // 虚析构函数
};

// 平面图形：圆形
class Circle : public Geometric_shape {
private:
    double radius;
public:
    Circle(double r) : radius(r) {}

    void Show() override {
        cout << "圆形：半径 = " << radius;
    }

    double perimeter() override {
        return 2 * M_PI * radius;
    }

    double area() override {
        return M_PI * radius * radius;
    }

    double volume() override {
        return 0;  // 平面图形体积为0
    }
};

// 平面图形：矩形
class Rectangle : public Geometric_shape {
private:
    double length, width;
public:
    Rectangle(double l, double w) : length(l), width(w) {}

    void Show() override {
        cout << "矩形：长 = " << length << "，宽 = " << width;
    }

    double perimeter() override {
        return 2 * (length + width);
    }

    double area() override {
        return length * width;
    }

    double volume() override {
        return 0;  // 平面图形体积为0
    }
};

// 平面图形：三角形
class Triangle : public Geometric_shape {
private:
    double a, b, c;
public:
    Triangle(double side1, double side2, double side3) : a(side1), b(side2), c(side3) {}

    void Show() override {
        cout << "三角形：边长 = " << a << "，" << b << "，" << c;
    }

    double perimeter() override {
        return a + b + c;
    }

    // 使用海伦公式计算三角形面积
    double area() override {
        double s = (a + b + c) / 2;
        return sqrt(s * (s-a) * (s-b) * (s-c));
    }

    double volume() override {
        return 0;  // 平面图形体积为0
    }
};

// 立体图形：长方体
class Box : public Geometric_shape {
private:
    double length, width, height;
public:
    Box(double l, double w, double h) : length(l), width(w), height(h) {}

    void Show() override {
        cout << "长方体：长 = " << length << "，宽 = " << width << "，高 = " << height;
    }

    // 底面（矩形）周长
    double perimeter() override {
        return 2 * (length + width);
    }

    // 底面（矩形）面积
    double area() override {
        return length * width;
    }

    double volume() override {
        return length * width * height;
    }
};

// 立体图形：圆柱体
class Cylinder : public Geometric_shape {
private:
    double radius, height;
public:
    Cylinder(double r, double h) : radius(r), height(h) {}

    void Show() override {
        cout << "圆柱体：半径 = " << radius << "，高 = " << height;
    }

    // 底面（圆）周长
    double perimeter() override {
        return 2 * M_PI * radius;
    }

    // 底面（圆）面积
    double area() override {
        return M_PI * radius * radius;
    }

    double volume() override {
        return M_PI * radius * radius * height;
    }
};

// 立体图形：圆锥体
class Cone : public Geometric_shape {
private:
    double radius, height;
public:
    Cone(double r, double h) : radius(r), height(h) {}

    void Show() override {
        cout << "圆锥体：半径 = " << radius << "，高 = " << height;
    }

    // 底面（圆）周长
    double perimeter() override {
        return 2 * M_PI * radius;
    }

    // 底面（圆）面积
    double area() override {
        return M_PI * radius * radius;
    }

    double volume() override {
        return (1.0/3) * M_PI * radius * radius * height;
    }
};

// 立体图形：三棱锥
class T_pyramid : public Geometric_shape {
private:
    double a, b, c, height;
public:
    T_pyramid(double side1, double side2, double side3, double h)
        : a(side1), b(side2), c(side3), height(h) {}

    void Show() override {
        cout << "三棱锥：底边长 = " << a << "，" << b << "，" << c << "，高 = " << height;
    }

    // 底面（三角形）周长
    double perimeter() override {
        return a + b + c;
    }

    // 底面（三角形）面积
    double area() override {
        double s = (a + b + c) / 2;
        return sqrt(s * (s-a) * (s-b) * (s-c));
    }

    double volume() override {
        double baseArea = area();
        return (1.0/3) * baseArea * height;
    }
};

// 立体图形：三棱柱
class T_prism : public Geometric_shape {
private:
    double a, b, c, height;
public:
    T_prism(double side1, double side2, double side3, double h)
        : a(side1), b(side2), c(side3), height(h) {}

    void Show() override {
        cout << "三棱柱：底边长 = " << a << "，" << b << "，" << c << "，高 = " << height;
    }

    // 底面（三角形）周长
    double perimeter() override {
        return a + b + c;
    }

    // 底面（三角形）面积
    double area() override {
        double s = (a + b + c) / 2;
        return sqrt(s * (s-a) * (s-b) * (s-c));
    }

    double volume() override {
        double baseArea = area();
        return baseArea * height;
    }
};

// 显示菜单
void showMenu() {
    cout << "\n=== 几何图形计算器 ===" << endl;
    cout << "请选择要创建的图形类型：" << endl;
    cout << "1. 圆形" << endl;
    cout << "2. 矩形" << endl;
    cout << "3. 三角形" << endl;
    cout << "4. 长方体" << endl;
    cout << "5. 圆柱体" << endl;
    cout << "6. 圆锥体" << endl;
    cout << "7. 三棱锥" << endl;
    cout << "8. 三棱柱" << endl;
    cout << "0. 退出程序" << endl;
    cout << "请输入选择（0-8）：";
}

// 创建圆形
Geometric_shape* createCircle() {
    double radius;
    cout << "请输入圆的半径：";
    cin >> radius;
    if (radius <= 0) {
        cout << "半径必须大于0！" << endl;
        return nullptr;
    }
    return new Circle(radius);
}

// 创建矩形
Geometric_shape* createRectangle() {
    double length, width;
    cout << "请输入矩形的长：";
    cin >> length;
    cout << "请输入矩形的宽：";
    cin >> width;
    if (length <= 0 || width <= 0) {
        cout << "长和宽必须大于0！" << endl;
        return nullptr;
    }
    return new Rectangle(length, width);
}

// 创建三角形
Geometric_shape* createTriangle() {
    double a, b, c;
    cout << "请输入三角形的三边长：" << endl;
    cout << "第一边：";
    cin >> a;
    cout << "第二边：";
    cin >> b;
    cout << "第三边：";
    cin >> c;

    // 验证三角形三边关系
    if (a <= 0 || b <= 0 || c <= 0) {
        cout << "边长必须大于0！" << endl;
        return nullptr;
    }
    if (a + b <= c || a + c <= b || b + c <= a) {
        cout << "输入的三边无法构成三角形！" << endl;
        return nullptr;
    }
    return new Triangle(a, b, c);
}

// 创建长方体
Geometric_shape* createBox() {
    double length, width, height;
    cout << "请输入长方体的长：";
    cin >> length;
    cout << "请输入长方体的宽：";
    cin >> width;
    cout << "请输入长方体的高：";
    cin >> height;
    if (length <= 0 || width <= 0 || height <= 0) {
        cout << "长、宽、高必须大于0！" << endl;
        return nullptr;
    }
    return new Box(length, width, height);
}

// 创建圆柱体
Geometric_shape* createCylinder() {
    double radius, height;
    cout << "请输入圆柱体的半径：";
    cin >> radius;
    cout << "请输入圆柱体的高：";
    cin >> height;
    if (radius <= 0 || height <= 0) {
        cout << "半径和高必须大于0！" << endl;
        return nullptr;
    }
    return new Cylinder(radius, height);
}

// 创建圆锥体
Geometric_shape* createCone() {
    double radius, height;
    cout << "请输入圆锥体的半径：";
    cin >> radius;
    cout << "请输入圆锥体的高：";
    cin >> height;
    if (radius <= 0 || height <= 0) {
        cout << "半径和高必须大于0！" << endl;
        return nullptr;
    }
    return new Cone(radius, height);
}

// 创建三棱锥
Geometric_shape* createTPyramid() {
    double a, b, c, height;
    cout << "请输入三棱锥底面三角形的三边长：" << endl;
    cout << "第一边：";
    cin >> a;
    cout << "第二边：";
    cin >> b;
    cout << "第三边：";
    cin >> c;
    cout << "请输入三棱锥的高：";
    cin >> height;

    // 验证三角形三边关系
    if (a <= 0 || b <= 0 || c <= 0 || height <= 0) {
        cout << "边长和高必须大于0！" << endl;
        return nullptr;
    }
    if (a + b <= c || a + c <= b || b + c <= a) {
        cout << "输入的三边无法构成三角形！" << endl;
        return nullptr;
    }
    return new T_pyramid(a, b, c, height);
}

// 创建三棱柱
Geometric_shape* createTPrism() {
    double a, b, c, height;
    cout << "请输入三棱柱底面三角形的三边长：" << endl;
    cout << "第一边：";
    cin >> a;
    cout << "第二边：";
    cin >> b;
    cout << "第三边：";
    cin >> c;
    cout << "请输入三棱柱的高：";
    cin >> height;

    // 验证三角形三边关系
    if (a <= 0 || b <= 0 || c <= 0 || height <= 0) {
        cout << "边长和高必须大于0！" << endl;
        return nullptr;
    }
    if (a + b <= c || a + c <= b || b + c <= a) {
        cout << "输入的三边无法构成三角形！" << endl;
        return nullptr;
    }
    return new T_prism(a, b, c, height);
}

// 显示图形信息和计算结果
void displayResults(Geometric_shape* shape) {
    if (shape == nullptr) return;

    cout << "\n--- 图形信息 ---" << endl;
    shape->Show();
    cout << endl;

    cout << "--- 计算结果 ---" << endl;
    double vol = shape->volume();
    if (vol == 0) {
        // 平面图形
        cout << "周长：" << shape->perimeter() << endl;
        cout << "面积：" << shape->area() << endl;
        cout << "体积：" << vol << "（平面图形）" << endl;
    } else {
        // 立体图形
        cout << "底面周长：" << shape->perimeter() << endl;
        cout << "底面面积：" << shape->area() << endl;
        cout << "体积：" << vol << endl;
    }
}

int main() {
    vector<unique_ptr<Geometric_shape>> shapes;
    int choice;

    cout << "欢迎使用几何图形计算器！" << endl;

    while (true) {
        showMenu();
        cin >> choice;

        if (choice == 0) {
            cout << "感谢使用，再见！" << endl;
            break;
        }

        Geometric_shape* shape = nullptr;

        switch (choice) {
            case 1:
                shape = createCircle();
                break;
            case 2:
                shape = createRectangle();
                break;
            case 3:
                shape = createTriangle();
                break;
            case 4:
                shape = createBox();
                break;
            case 5:
                shape = createCylinder();
                break;
            case 6:
                shape = createCone();
                break;
            case 7:
                shape = createTPyramid();
                break;
            case 8:
                shape = createTPrism();
                break;
            default:
                cout << "无效的选择，请重新输入！" << endl;
                continue;
        }

        if (shape != nullptr) {
            displayResults(shape);
            shapes.push_back(unique_ptr<Geometric_shape>(shape));

            cout << "\n是否继续创建其他图形？(y/n): ";
            char continueChoice;
            cin >> continueChoice;
            if (continueChoice != 'y' && continueChoice != 'Y') {
                break;
            }
        }
    }

    // 显示所有创建的图形汇总
    if (!shapes.empty()) {
        cout << "\n=== 本次创建的所有图形汇总 ===" << endl;
        for (size_t i = 0; i < shapes.size(); i++) {
            cout << "\n第" << (i + 1) << "个图形：";
            shapes[i]->Show();
            cout << endl;
        }
    }

    return 0;
}